
<sql splitStatements="false">
    <![CDATA[
CREATE OR REPLACE FUNCTION sandf.fn_get_deviations_RTQ(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    max_deviations_per_carrier INTEGER DEFAULT 4,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$

DECLARE
    quote_record RECORD;
    carrier_deviations_map JSONB := '{}'::jsonb;
    result_pages JSONB := '[]'::jsonb;
    quote_count INTEGER := 0;
    MAX_DEVIATIONS_PER_CARRIER INTEGER := max_deviations_per_carrier;
    carrier_name TEXT;
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    carrier_order INTEGER;
    deviations_array JSONB;
    deviation_idx INTEGER;
    total_deviations INTEGER;
    current_deviation_page JSONB;
    -- Use global resolver instead of duplicating logic
    resolved_employee_class TEXT;

    -- Variable to track the first carrier name for display purposes
    first_carrier_name TEXT;
    display_carrier_name TEXT;

BEGIN
    -- Use global resolver to get the appropriate employee class
    -- This handles both single RTQ and multi-class scenarios automatically
    resolved_employee_class := sandf.fn_get_resolved_employee_class(plan_uuid_param);

    -- First, count the total number of quotes
    SELECT COUNT(DISTINCT q.quote_id) INTO quote_count
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ec.name = resolved_employee_class
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb
    AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid));

    -- Apply condition: if more than 4 quotes, set max deviations per carrier to 2

    -- Build carrier order map and collect deviations from all carriers
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               c.name as carrier_name,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = resolved_employee_class
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
    LOOP
        carrier_name := quote_record.carrier_description;

        -- Get carrier order using existing function
        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_record.quote_id,
            quote_record.quote_uuid,
            999999
        );

        -- Build carrier order map
        IF NOT carrier_order_map ? carrier_name THEN
            carrier_order_map := carrier_order_map || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'order', carrier_order,
                    'quote_id', quote_record.quote_id,
                    'quote_uuid', quote_record.quote_uuid
                )
            );
        END IF;

        -- Extract deviations for this carrier
        deviations_array := quote_record.formatted_quote_details -> 'deviations';

        IF deviations_array IS NOT NULL AND jsonb_typeof(deviations_array) = 'array' THEN
            -- Store deviations for this carrier
            carrier_deviations_map := carrier_deviations_map || jsonb_build_object(
                carrier_name, deviations_array
            );
        END IF;
    END LOOP;

    -- Order carriers based on user preferences and capture first carrier name
    FOR carrier_item IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (value ->> 'order')::integer ASC, key ASC
    LOOP
        -- Capture the first carrier name for display purposes
        IF first_carrier_name IS NULL THEN
            first_carrier_name := carrier_item;
        END IF;

        -- Use "Current" for the first carrier, otherwise use actual name
        IF carrier_item = first_carrier_name THEN
            display_carrier_name := 'Current';
        ELSE
            display_carrier_name := carrier_item;
        END IF;

        ordered_carriers_array := ordered_carriers_array || jsonb_build_array(display_carrier_name);
    END LOOP;

    -- Create flat structure with 4 deviations per page
    DECLARE
        carrier_names_array TEXT[];
        current_carrier_name TEXT;
        all_deviations JSONB := '[]'::jsonb;
        current_page_data JSONB;
        deviations_per_page INTEGER := 6;
        total_deviations_count INTEGER;
        page_start_idx INTEGER;
        page_end_idx INTEGER;
        deviation_obj JSONB;
    BEGIN
        -- Convert ordered carriers to array for easier indexing
        SELECT array_agg(value::text ORDER BY ordinality)
        INTO carrier_names_array
        FROM jsonb_array_elements_text(ordered_carriers_array) WITH ORDINALITY;

        -- Check if carrier_names_array is NULL and handle gracefully
        IF carrier_names_array IS NULL THEN
            RAISE NOTICE 'No carriers found in ordered_carriers_array';
            RETURN '[]'::jsonb;
        END IF;

        -- Create flat array of all deviations with carrier names
        FOR carrier_idx IN 1..array_length(carrier_names_array, 1) LOOP
            current_carrier_name := carrier_names_array[carrier_idx];

            -- Find the original carrier name to get deviations data
            -- Since carrier_names_array now contains display names, we need to map back
            DECLARE
                original_carrier_name TEXT;
            BEGIN
                -- If this is "Current", find the first carrier name, otherwise use as-is
                IF current_carrier_name = 'Current' THEN
                    original_carrier_name := first_carrier_name;
                ELSE
                    original_carrier_name := current_carrier_name;
                END IF;

                deviations_array := COALESCE(carrier_deviations_map -> original_carrier_name, '[]'::jsonb);

                -- Add each deviation as a separate object
                -- Only process if deviations_array is actually an array
                IF deviations_array IS NOT NULL AND jsonb_typeof(deviations_array) = 'array' THEN
                    FOR deviation_idx IN 0..jsonb_array_length(deviations_array) - 1 LOOP
                        deviation_obj := jsonb_build_object(
                            'name', current_carrier_name,  -- Use display name (Current or actual name)
                            'deviations', deviations_array -> deviation_idx
                        );
                        all_deviations := all_deviations || jsonb_build_array(deviation_obj);
                    END LOOP;
                END IF;
            END;
        END LOOP;

        -- Create pages with exactly 4 deviations each
        -- Ensure all_deviations is a valid array before getting its length
        IF all_deviations IS NULL OR jsonb_typeof(all_deviations) != 'array' THEN
            all_deviations := '[]'::jsonb;
        END IF;
        total_deviations_count := jsonb_array_length(all_deviations);

        -- Create pages with 4 deviations each
        FOR page_start_idx IN 0..total_deviations_count-1 BY deviations_per_page LOOP
            page_end_idx := LEAST(page_start_idx + deviations_per_page - 1, total_deviations_count - 1);
            current_page_data := '[]'::jsonb;

            -- Add deviations to current page
            FOR deviation_idx IN page_start_idx..page_end_idx LOOP
                current_page_data := current_page_data || jsonb_build_array(
                    all_deviations -> deviation_idx
                );
            END LOOP;

            -- Add page to results
            result_pages := result_pages || jsonb_build_array(
                jsonb_build_object(
                    'carrierData', current_page_data
                )
            );
        END LOOP;
    END;

    -- Return array of paginated results
    RETURN result_pages;
END;
$$;
]]>
        </sql>